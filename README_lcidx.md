# LCIDX.LII File Parser

This package provides Python functions to read and write TDX `lcidx.lii` files, which contain stock codes and names in a binary format.

## File Format

The `lcidx.lii` file uses a fixed-length record format:
- Each record is 64 bytes
- Record structure:
  - Bytes 0-3: Header (0x01000000 for valid records)
  - Bytes 4-9: Stock code (6 bytes, ASCII)
  - Bytes 10-29: Stock name (20 bytes, GBK encoded)
  - Bytes 30-63: Padding/other data

## Files

- `lcidx_parser.py` - Core parser with read/write functions
- `lcidx_utils.py` - Additional utility functions
- `test_lcidx.py` - Test script

## Basic Usage

### Reading a file

```python
from lcidx_parser import read_lcidx_file

# Read the file
records = read_lcidx_file(r"C:\new_tdx\T0002\lc\lcidx.lii")

# Each record is a dictionary with 'code' and 'name' keys
for record in records:
    print(f"Code: {record['code']}, Name: {record['name']}")
```

### Writing a file

```python
from lcidx_parser import write_lcidx_file

# Prepare data - can use dictionaries or lists
records = [
    {'code': '000001', 'name': '平安银行'},
    {'code': '000002', 'name': '万科A'},
    ['000003', '国农科技'],  # List format also works
]

# Write to file
write_lcidx_file('output.lii', records)
```

### Using the class interface

```python
from lcidx_parser import LcidxParser

# Create parser instance
parser = LcidxParser(r"C:\new_tdx\T0002\lc\lcidx.lii")

# Read records
records = parser.read_file()

# Add a new record
parser.add_record('600000', '浦发银行')

# Write records
parser.write_file(records)
```

## Utility Functions

### Search functions

```python
from lcidx_utils import search_by_code, search_by_name

# Search by stock code
result = search_by_code('file.lii', '393001')
if result:
    print(f"Found: {result['name']}")

# Search by name (partial matching)
results = search_by_name('file.lii', '储能')
for result in results:
    print(f"Code: {result['code']}, Name: {result['name']}")
```

### File information

```python
from lcidx_utils import print_file_info

# Print file statistics
print_file_info(r"C:\new_tdx\T0002\lc\lcidx.lii")
```

### DataFrame integration (requires pandas)

```python
from lcidx_utils import lcidx_to_dataframe, dataframe_to_lcidx
import pandas as pd

# Convert to DataFrame
df = lcidx_to_dataframe('file.lii')
print(df.head())

# Save DataFrame to file
dataframe_to_lcidx(df, 'output.lii')
```

### Merge multiple files

```python
from lcidx_utils import merge_lcidx_files

# Merge multiple files
file_paths = ['file1.lii', 'file2.lii', 'file3.lii']
merge_lcidx_files(file_paths, 'merged.lii', remove_duplicates=True)
```

## Data Format

### Input formats for write functions

The write functions accept records in multiple formats:

1. **Dictionary format** (recommended):
   ```python
   {'code': '000001', 'name': '平安银行'}
   ```

2. **List/tuple format**:
   ```python
   ['000001', '平安银行']
   ('000001', '平安银行')
   ```

### Output format

All read functions return a list of dictionaries:
```python
[
    {'code': '000001', 'name': '平安银行'},
    {'code': '000002', 'name': '万科A'},
    # ...
]
```

## Error Handling

The functions handle various error conditions:
- File not found
- Invalid file format
- Encoding errors (GBK/ASCII)
- Truncated files

## Limitations

- Stock codes are limited to 6 ASCII characters
- Stock names are limited to 20 bytes in GBK encoding (typically 10 Chinese characters)
- File size is padded to multiples of 64 bytes

## Example

```python
#!/usr/bin/env python3
from lcidx_parser import read_lcidx_file, write_lcidx_file

# Read existing file
file_path = r"C:\new_tdx\T0002\lc\lcidx.lii"
records = read_lcidx_file(file_path)

print(f"Found {len(records)} records:")
for record in records:
    print(f"  {record['code']}: {record['name']}")

# Add new records
new_records = records + [
    {'code': '600000', 'name': '浦发银行'},
    {'code': '600036', 'name': '招商银行'},
]

# Write to new file
write_lcidx_file('updated.lii', new_records)
print("Updated file saved as 'updated.lii'")
```

## Testing

Run the test script to verify functionality:

```bash
python test_lcidx.py
```

This will test reading, writing, and various utility functions.
