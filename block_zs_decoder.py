from ast import dump
import struct
import os
import binascii

def hex_dump(data, max_bytes=100):
    """Convert binary data to hex representation"""
    return binascii.hexlify(data[:max_bytes]).decode('ascii')

def decode_block_zs(file_path):
    blocks = []
    
    with open(file_path, 'rb') as f:
        # Get total file size for debugging
        f.seek(0, os.SEEK_END)
        file_size = f.tell()
        f.seek(0)
        print(f"Total file size: {file_size} bytes")
        
        # Read and verify header
        header = f.read(8)
        print(f"File header (hex): {hex_dump(header)}")
        if header != b'Registry':
            print(f"Warning: Unexpected header: {header}")
        
        # Diagnostic information
        current_position = 0
        block_count = 0  # Add a block count for debugging
        
        while current_position < file_size:
            # Current file position
            current_position = f.tell()
            print(f"\nCurrent file position: {current_position}")
            
            # Try to read block name length
            name_len_bytes = f.read(4)
            if not name_len_bytes or len(name_len_bytes) < 4:
                print("Reached end of name length bytes")
                break
            
            # Print raw bytes for diagnosis
            print(f"Name length bytes (hex): {name_len_bytes}")
            
            try:
                name_len = struct.unpack('>I', name_len_bytes)[0]
                print(f"Block name length: {name_len}")
            except struct.error:
                print(f"Error unpacking name length: {hex_dump(name_len_bytes)}")
                break
            
            # Read block name
            try:
                block_name_bytes = f.read(name_len)
                block_name = block_name_bytes.decode('gbk', errors='replace').strip()
                print(f"Block name: {block_name}")
                # print(f"Block name bytes (hex): {hex_dump(block_name_bytes)}")
            except Exception as e:
                print(f"Error reading block name: {e}")
                break

            return block_name
            
            # Read number of stocks
            stock_count_bytes = f.read(4)
            if not stock_count_bytes or len(stock_count_bytes) < 4:
                print("Reached end of file or incomplete stock count")
                break
            
            try:
                stock_count = struct.unpack('<I', stock_count_bytes)[0]
                print(f"Number of stocks: {stock_count}")
                print(f"Stock count bytes (hex): {hex_dump(stock_count_bytes)}")
            except struct.error:
                print(f"Error unpacking stock count: {hex_dump(stock_count_bytes)}")
                break
            
            # Read stock codes
            stocks = []
            for i in range(stock_count):
                stock_code_bytes = f.read(6)
                if not stock_code_bytes:
                    print(f"Incomplete stock code at index {i}")
                    break
                stock_code = stock_code_bytes.decode('ascii', errors='ignore').strip()
                stocks.append(stock_code)
                print(f"Stock {i+1}: {stock_code} (hex: {hex_dump(stock_code_bytes)})")
            
            # Only append block if stocks were successfully read
            if stocks:
                block_count += 1  # Increment block count
                blocks.append({
                    'block_name': block_name,
                    'stocks': stocks
                })
                print(f"Added block {block_count}: {block_name} with {len(stocks)} stocks")
    
    # print(f"\nTotal blocks processed: {block_count}")
    # return blocks

# Usage
file_path = r'C:/new_tdx_mock/T0002/hq_cache/block_gn.dat'

# Check if file exists
if not os.path.exists(file_path):
    print(f"Error: File not found at {file_path}")
else:
    try:
        decoded_blocks = decode_block_zs(file_path)

        # # Convert list of dictionaries to a dictionary
        # blocks_dict = {block['block_name']: block['stocks'] for block in decoded_blocks}

        # # Print results
        # print("\n--- Decoding Summary ---")
        # print(f"Total blocks decoded: {len(blocks_dict)}")
        # for block_name, stocks in blocks_dict.items():
        #     print(f"Block: {block_name}")
        #     print(f"Stocks ({len(stocks)}): {', '.join(stocks)}")
        #     print("-" * 50)
        
        # Return the dictionary for further use
        # return blocks_dict
    except Exception as e:
        print(f"An error occurred during decoding: {e}")
        # return {}