from flask import Flask, render_template, request, jsonify
import pandas as pd


import os
import numpy as np
import pandas as pd
import tkinter as tk

import os, sys, time, struct
import datetime, random
import pandas as pd, numpy as np

from pytdx.hq import TdxHq_API
# from pytdx.config.hosts import hq_hosts

app = Flask(__name__)

api = TdxHq_API()
hq_hosts = pd.read_pickle("good_hosts.pkl").tolist()
sh_hosts = [s for s in hq_hosts if "上海" in s[0]]


class SFSJ:
    def __init__(
        self,
    ):
        pass

    def fetch_sfsj(self, cfg: dict):
        tdx_dir = cfg.get("tdx_dir")
        cloud_cache_file = cfg.get("cloud_cache_file")
        a = pd.read_json(f"{tdx_dir}T0002/cloud_cache/list/{cloud_cache_file}", encoding="gbk")
        cols = np.array(a["colheader"].values[0])
        b = np.array(a["data"].values[0])
        df = pd.DataFrame(b, columns=cols)
        df = df.loc[(~df.yy.str.contains("ST")) & (df["$SC"] != "")]
        sf = df[["$SC", "$ZQDM", "ztsj1"]].copy()
        sf["ztsj2"] = sf["ztsj1"].str.replace(":", "")
        sf.columns = ["mkt", "sym", "str", "value"]
        return sf

    def save_sfsj(self, save_dir: str, sf_df: pd.DataFrame, save_as_name: str):
        assert len(save_as_name) > 0, "save_as_name not provided!"
        sf_df.to_csv(f"{save_dir}{save_as_name}", header=None, index=None, sep="|")
        now = datetime.datetime.now().strftime("%H:%M:%S")
        print(f"[{now}] sbsj file saved: {save_dir}{save_as_name}")

    def update_sfsj(self, cfg: dict):
        tdx_dir = cfg.get("tdx_dir")
        save_dir = cfg.get("save_dir")

        sf_df = self.fetch_sfsj(cfg)
        print(sf_df)
        today = str(datetime.date.today())
        if pd.to_datetime(today).date().weekday() < 5:
            self.save_sfsj(save_dir, sf_df, f"daily_{today}_sfsj.txt")
        else:
            last_bday = (pd.to_datetime(today) - pd.offsets.BDay(1)).strftime("%Y-%m-%d")
            if not os.path.exists(f"{save_dir}daily_{last_bday}_sfsj.txt"):
                self.save_sfsj(save_dir, sf_df, f"daily_{last_bday}_sfsj.txt")
        return sf_df


class Data:
    def __init__(self):
        pass

    ## read all syms each day
    def read_stk_syms_from_tdxdir(self, tdx_dir: str, markets: list):
        all_syms = []
        for market in markets:
            daily_dir = f"{tdx_dir}{market}/lday/"
            all_syms.extend([s.split(".")[0][2:] for s in os.listdir(daily_dir)])
        return [s for s in all_syms if s[:2] in ["00", "30", "60", "68", "87", "83", "43", "92"]]

    ## download minBar from TDX server
    def download_1sym_1minbar(self, host, sym, market, ndays, offset=0, verbose=False):
        all_data = []
        with api.connect(host[1], host[2]):
            for i in range(ndays):
                num_bars = 240 if i < ndays - 1 else 241
                if sym.startswith("88"):
                    data = api.to_df(api.get_index_bars(8, 1, sym, 240 * (offset + i), num_bars))
                else:
                    data = api.to_df(api.get_security_bars(8, market, sym, 240 * (offset + i), num_bars))
                if data is None:
                    continue
                try:
                    data = data[["datetime", "open", "close", "high", "low", "vol", "amount"]]
                except:
                    continue
                all_data.append(data)
                if verbose:
                    print(f"downloading [{sym}]: {100 * (i + 1) / ndays:.2f} %", end="\n")
                # time.sleep(0.1)

        if len(all_data) == 0:
            return None
        mgd = pd.concat(all_data[::-1])
        mgd["datetime"] = pd.to_datetime(mgd["datetime"])
        mgd["date"] = mgd["datetime"].dt.date
        return mgd.set_index(["date", "datetime"])["close"]

    ## batch download minBar for all sym over multi-days
    def batch_download(self, syms, out_datas={}, ndays=1, offset=0, retry_interval=300):
        market_map = {
            "00": 0,
            "30": 0,  ## shenzhen market
            "60": 1,
            "68": 1,
            "88": 1,  ## shanghai market
            "43": 2,
            "83": 2,
            "87": 2,
            "92": 2,
        }  ## beijing market
        # for i in range(retry_num):
        host = random.choice(sh_hosts)
        num_trial = 1
        while True:
            try:
                now = datetime.datetime.now().strftime("%H:%M:%S")
                print(f"[{now}] host: ", host)
                for sym in syms:
                    t0 = time.time()
                    if sym in out_datas.keys():
                        continue
                    market = market_map[sym[:2]]
                    out_datas[sym] = self.download_1sym_1minbar(host, sym, market, ndays=ndays, offset=offset, verbose=False)
                    pct = 100 * (syms.index(sym) + 1) / len(syms)
                    t1 = time.time()
                    time_remaining = (len(syms) - len(out_datas)) * (t1 - t0) / 60.0
                    now = datetime.datetime.now().strftime("%H:%M:%S")
                    print(
                        f"[{now}] [ try_time {num_trial} ] progress: {pct:.2f} %, time elapsed: {time_remaining:.2f} minutes",
                        end="\r",
                    )
                break
            except:
                if len(out_datas) == 0:
                    sh_hosts.remove(host)
                    if len(sh_hosts) == 0:
                        host = random.choice(hq_hosts)
                    else:
                        host = random.choice(sh_hosts)
                    continue
                print()
                for i in range(retry_interval):
                    print(f"time elapsed to retry again: {i} / {retry_interval}", end="\r")
                    time.sleep(1)
                num_trial += 1

    ## save results
    def save_as_tdxfile(self, speed_df, save_dir="./", name_comment=""):
        def _market_map(sym):
            if sym[:2] in ["00", "30"]:
                return 0
            elif sym[:2] in ["60", "68", "88"]:
                return 1
            elif sym[:2] in ["43", "83", "87", "92"]:
                return 2

        xs = []
        mgd = "mgd_" if speed_df.shape[0] > 1 else "daily_"
        for date in speed_df.index:
            x = speed_df.loc[date].dropna().to_frame().reset_index()
            x.columns = ["sym", "value"]
            x["mkt"] = x["sym"].apply(_market_map)
            x["date"] = str(date).replace("-", "")
            x = x[["mkt", "sym", "date", "value"]]
            xs.append(x)
        pd.concat(xs).to_csv(
            f"{save_dir}{mgd}{date}_{name_comment}.txt",
            sep="|",
            header=None,
            index=None,
        )
        now = datetime.datetime.now().strftime("%H:%M:%S")
        print(f"[{now}] data saved: {f'{save_dir}{mgd}{date}_{name_comment}.txt'}")

    def load_speed_data(self, data_root):
        files = [s for s in os.listdir(data_root) if s.endswith("min5.txt")]
        datas = []
        for file in files:
            datas.append(pd.read_csv(f"{data_root}{file}", header=None, sep="|", dtype="S"))
        return pd.concat(datas)

    def download(self, cfg: dict):
        tdx_dir = cfg.get("tdx_dir")
        markets = ["sz", "sh", "bj"]
        hssyms = self.read_stk_syms_from_tdxdir(f"{tdx_dir}vipdoc/", markets=markets)
        zspath = f"{tdx_dir}T0002/hq_cache/tdxzs.cfg"
        zssyms = pd.read_csv(zspath, header=None, sep="|", dtype="S", encoding="gbk")[1].tolist()
        all_syms = hssyms + zssyms

        datas = {}
        ndays, offset = 1, 0
        self.batch_download(all_syms[:], datas, ndays=ndays, offset=offset, retry_interval=60)
        datas = {key: value for key, value in datas.items() if value is not None}
        now = datetime.datetime.now().strftime("%H:%M:%S")
        print(f"\n[{now}] downloaded items: ", len(datas))
        return datas


class Speed(Data):
    def __init__(self):
        super().__init__()

    ## calc_max rise speed from minBar close
    def calc_max_spped(self, ndays, close, period):
        speed = close.pct_change(period, fill_method=None).dropna(how="all").groupby("date").max() * 100
        return speed.iloc[-ndays:, :]

    def update_spdfile(self, datas: dict, cfg: dict):
        save_dir = cfg.get("save_dir")
        close = pd.concat(datas, axis=1, keys=datas.keys()).sort_index()
        speed5 = self.calc_max_spped(cfg.get("lookback_days"), close, 5)
        speed5 = speed5.loc[~speed5["000001"].isna()]  ## filter out NAN rows for past dates
        self.save_as_tdxfile(speed5, save_dir=save_dir, name_comment="spd_min5")
        now = datetime.datetime.now().strftime("%H:%M:%S")
        print(f"[{now}] max min5 speed fetched and saved!")
        return speed5


class Operator(Data):
    def __init__(self):
        super().__init__()

    ## merge sfsj and spped to update tdx extern_user.txt
    def update_extern_user(self, target_file: str, data_dir: str):
        filetag = sorted([s for s in os.listdir(data_dir) if s.endswith("sfsj.txt")])[-1]
        filetag2 = sorted([s for s in os.listdir(data_dir) if s.endswith("spd_min5.txt") and s.startswith("daily")])[-1]

        sf = pd.read_csv(f"{data_dir}{filetag}", header=None, sep="|", dtype=str)
        sf[4] = "1"  ## 自定义数据代号1：首封时间
        spd = pd.read_csv(f"{data_dir}{filetag2}", header=None, sep="|", dtype=str)
        spd[4] = "3"  ## 自定义数据代号3：三度速度

        # speed_root = 'D:/lin/400其他资料/sbsj/fetched_data/'
        speeds = self.load_speed_data(data_dir)
        speeds[3] = speeds[3].astype(float)
        # max_spd = speeds[speeds[2].isin(sorted(speeds[2].unique())[-7:])].groupby(1).sum().reset_index()
        max_spd5 = speeds[speeds[2].isin(sorted(speeds[2].unique())[-5:])].groupby(1).agg({2: "max", 3: "sum", 0: "last"}).reset_index()
        max_spd3 = speeds[speeds[2].isin(sorted(speeds[2].unique())[-3:])].groupby(1).agg({2: "max", 3: "sum", 0: "last"}).reset_index()

        max_spd5[4] = "7"
        max_spd3[4] = "8"

        result = pd.concat([sf, spd, max_spd5, max_spd3])
        result = result[[0, 1, 4, 2, 3]]
        print(result)
        result.to_csv(target_file, sep="|", header=None, index=None)
        print("updating external_user.txt finished!")

    def update_tdx_signal_file(self, cfg: dict):
        datas = self.load_speed_data(cfg.get("save_dir"))
        dates = sorted(datas[2].unique())[-cfg.get("lookback_days") :]
        sample = datas.loc[datas[2].isin(dates)].sort_values([2, 1]).drop_duplicates()

        tdx_signal_dir = cfg.get("tdx_signal_dir")
        gpd = sample.groupby(1)

        syms = sorted(sample[1].unique())
        for sym in syms:
            group = gpd.get_group(sym)
            group_values = group.values
            market = group[0].unique()[0]
            file_name = f"{tdx_signal_dir}{market}_{sym}.dat"
            with open(file_name, "wb") as file:
                for i in range(group_values.shape[0]):
                    value = group_values[i, -2:]
                    file.write(struct.pack("if", int(value[0]), float(value[1])))
            now = datetime.datetime.now().strftime("%H:%M:%S")
            print(
                f"[{now}] progress [update signal_user_5]: {syms.index(sym) + 1}/{len(syms)}",
                end="\r",
            )

        now = datetime.datetime.now().strftime("%H:%M:%S")
        # input(f"\n[{now}] press any key to exit!")


# ---------------------------------------


class BlockDecoder:
    def __init__(self, cfg: dict):
        self.cfg = cfg

    def read_infoharbor_block(self, filepath: str):
        with open(filepath, "r") as file:
            return file.readlines()

    def decode_infoharbor_block(self, contents: list):
        results = []
        ## init a series of variables
        blockname, num_stock, blockcode, sdate, edate, stocks = [""] * 5 + [[]]

        ## iterate over all lines to parse
        for i, line in enumerate(contents):
            values = line.split(",")
            if values[0].startswith("#"):  ## new block is coming
                if len(stocks) > 0:  ## if last block is effective, add to results
                    results.append((blockname, num_stock, blockcode, sdate, edate, stocks))

                ## update variables with new block info
                blockname, num_stock, blockcode, sdate, edate, stocks = values[:5] + [[]]
            else:
                stocks += values[:-1]  ## add stocks

        cols = ["blockname", "num_stock", "blockcode", "sdate", "edate", "stocks"]
        results = pd.DataFrame(results, columns=cols)
        results = results.loc[results["blockcode"].str.len() > 0]  ## filter out invalid blocks
        results["stocks"] = results["stocks"].apply(lambda x: [s.replace("#", "") for s in x])
        return results

    def code_map(self, code):
        map_ = {
            "00": "0",
            "30": "0",
            "60": "1",
            "68": "1",
            "43": "2",
            "83": "2",
            "87": "2",
            "92": "2",
        }
        if isinstance(code, str):
            return f"{map_[code[0:2]]}{code}"

    def decode_hy(self, cfg: dict):
        hycfg_path = cfg.get("hycfg_path")
        zscfg_path = cfg.get("zscfg_path")
        b = pd.read_csv(hycfg_path, dtype=str, sep="|", header=None)
        b.columns = ["a1", "stocks", "tmp", "a2", "a3", "a4"]
        b = b.loc[~b["tmp"].isna()]
        b["tmp"] = b["tmp"].str[:5]
        c = pd.read_csv(zscfg_path, dtype=str, sep="|", encoding="gbk", header=None)
        c.columns = ["blockname", "blockcode", "b1", "b2", "b3", "tmp"]
        c = c.loc[c["blockcode"].str.startswith("880")]
        mgd = b.merge(c, on="tmp", how="outer")
        mgd = mgd.loc[~mgd["tmp"].isna()]
        hyblock = mgd.groupby("blockname").agg({"blockcode": "last", "stocks": lambda x: x.tolist()}).reset_index()
        hyblock = hyblock.loc[hyblock["stocks"].str.len() > 2]
        hyblock["sdate"] = ""
        hyblock["edate"] = ""
        hyblock["num_stock"] = hyblock["stocks"].apply(lambda x: len(x))
        cols = ["blockname", "num_stock", "blockcode", "sdate", "edate", "stocks"]
        hyblock["stocks"] = hyblock["stocks"].apply(lambda x: [s for s in x if isinstance(s, str)])
        hyblock["stocks"] = hyblock["stocks"].apply(lambda x: [self.code_map(s) for s in x])
        return hyblock[cols]

    def filter_fcblock(self, block: pd.DataFrame, rxbk_path: str):
        rxbk = pd.read_csv(rxbk_path, sep="\t", header=None, dtype=str)[0].str[1:].tolist()
        sel = block.loc[block["blockcode"].isin(rxbk)]
        stocks = []
        for s in sel["stocks"].tolist():
            stocks.extend(s)
        return sorted(set(stocks)), sel

    def update_fcbk(self, fc_stocks: list, fcbk_path: str):
        with open(fcbk_path, "w") as f:
            f.write("\n")
            for s in fc_stocks[:]:
                f.write(f"{s}\n")
        print(f"SUCCESS, updated stocks: {len(fc_stocks)}")

    def read_banzhi(self):
        # banzhi_block_path = f'{self.cfg.get("block_root")}BZ.blk'
        banzhi_block_path = f"{self.cfg.get('block_root')}RX.blk"
        banzhi = pd.read_csv(banzhi_block_path, header=None, dtype=str)[0].tolist()
        return [blk[1:] for blk in banzhi]

    def union(self, *args):
        from functools import reduce

        unioned = reduce(lambda x, y: set(x).union(set(y)), args)
        return sorted(unioned)

    def read_guanzhu(self):
        block_root = self.cfg.get("block_root")
        guanzhu = pd.read_csv(f"{block_root}XJW.blk", header=None, dtype=str)[0].tolist()
        lz123 = pd.read_csv(f"{block_root}LZ123.blk", header=None, dtype=str)[0].tolist()
        dingjin = pd.read_csv(f"{block_root}DJ.blk", header=None, dtype=str)[0].tolist()
        return self.union(guanzhu, lz123, dingjin)

    def read_block_info(self):
        lines = self.read_infoharbor_block(self.cfg.get("infoharbor_path"))
        gfblock = self.decode_infoharbor_block(lines)
        hyblock = self.decode_hy(self.cfg)
        return pd.concat([gfblock, hyblock])

    def block_intersection(self, block_name: str):
        guanzhu = self.read_guanzhu()
        banzhi = self.read_banzhi()
        block = self.read_block_info().set_index("blockcode")
        self.block = block.loc[banzhi]
        sel_stocks = block.loc[block["blockname"] == block_name]["stocks"].tolist()[0]
        return sorted(set(guanzhu).intersection(set(sel_stocks)))

    def block_intersection_sc(self, block_name: str):
        block_root = self.cfg.get("block_root")
        guanzhu = self.read_guanzhu()
        sc_stocks = pd.read_csv(f"{block_root}SC.blk", header=None, dtype=str)[0].tolist()
        sel_stocks = list(set(guanzhu).intersection(sc_stocks))
        # banzhi = self.read_banzhi()
        # block = self.read_block_info().set_index("blockcode")
        # self.block = block.loc[banzhi]
        # sel_stocks = block.loc[block["blockname"] == block_name]["stocks"].tolist()[0]
        sel_stocks = [stk for stk in sel_stocks if stk[1:3] in {'30', '68'}]
        return sorted(sel_stocks)
        # return sorted(set(guanzhu).intersection(set(sel_stocks)))

    def block_intersection_bz(self, block_name: str):
        # guanzhu = self.read_guanzhu()
        banzhi = self.read_banzhi()
        block = self.read_block_info().set_index("blockcode")
        self.block = block.loc[banzhi]
        sel_stocks = block.loc[block["blockname"] == block_name]["stocks"].tolist()[0]
        sel_stocks = [stk for stk in sel_stocks if stk[1:3] in {'87', '43', '92', '83'}]
        return sorted(sel_stocks)
        # return sorted(set(guanzhu).intersection(set(sel_stocks)))


    def apply_xuangu(self, selected_stocks: list):
        xuangu_block_path = os.path.join(self.cfg.get("block_root"), f"XG.blk")
        with open(xuangu_block_path, "w") as file:
            for stock in selected_stocks:
                file.write(f"{stock}\n")


cfg = {
    "infoharbor_path": r"C:/new_tdx/T0002/hq_cache/infoharbor_block.dat",
    "hycfg_path": r"C:/new_tdx/T0002/hq_cache/tdxhy.cfg",
    "zscfg_path": r"C:/new_tdx/T0002/hq_cache/tdxzs3.cfg",
    "rxbk_path": r"C:/new_tdx/T0002/blocknew/RX.blk",
    "fcbk_path": r"c:/new_tdx/T0002/blocknew/FC.blk",
    "block_root": r"c:/new_tdx/T0002/blocknew/",
}

bd = BlockDecoder(cfg)


@app.route("/")
def index():
    # 获取板块名称列表
    block = bd.read_block_info().set_index("blockcode")
    banzhi = bd.read_banzhi()
    block_data = block.loc[banzhi]
    options = block_data["blockname"].tolist()
    return render_template("index.html", options=options)


@app.route("/select_stocks", methods=["POST"])
def select_stocks():
    block_name = request.form.get("block_name")
    selected_stocks = bd.block_intersection(block_name)
    bd.apply_xuangu(selected_stocks)

    return jsonify(
        {
            "status": "success",
            "block_name": block_name,
            # 'selected_stocks': selected_stocks,
            "count": len(selected_stocks),
        }
    )

@app.route("/select_sc_stocks", methods=["POST"])
def select_sc_stocks():
    block_name = request.form.get("block_name")
    selected_stocks = bd.block_intersection_sc(block_name)
    bd.apply_xuangu(selected_stocks)

    return jsonify(
        {
            "status": "success",
            "block_name": block_name,
            # 'selected_stocks': selected_stocks,
            "count": len(selected_stocks),
        }
    )

@app.route("/select_bz_stocks", methods=["POST"])
def select_bz_stocks():
    block_name = request.form.get("block_name")
    selected_stocks = bd.block_intersection_bz(block_name)
    bd.apply_xuangu(selected_stocks)

    return jsonify(
        {
            "status": "success",
            "block_name": block_name,
            # 'selected_stocks': selected_stocks,
            "count": len(selected_stocks),
        }
    )


@app.route("/update_fengchui", methods=["POST"])
def update_fengchui():
    block = bd.read_block_info()  # .set_index("blockcode")
    fc_stocks, sel_block = bd.filter_fcblock(block, cfg.get("rxbk_path"))
    bd.update_fcbk(fc_stocks, cfg.get("fcbk_path"))
    print(len(fc_stocks))

    return jsonify({"status": "success", "count": len(fc_stocks)})


@app.route("/data_fetcher", methods=["POST"])
def data_fetcher():
    sfsj = SFSJ()
    data = Data()
    spder = Speed()
    op = Operator()

    cfg = {
        "tdx_dir": r"C:/new_tdx/",
        "sfsj_saveroot": r"D:/lin/400其他资料/scripts/fetched_data/",
        "cloud_cache_file": r"func_zdtfx101_1.jsn",
        "save_dir": r"D:/lin/400其他资料/scripts/fetched_data/",
        "tdx_signal_dir": r"C:/new_tdx/T0002/signals/signals_user_5/",
        "tdx_extern_file": r"C:/new_tdx/T0002/signals/extern_user.txt",
        "lookback_days": 250,
    }

    sf_df = sfsj.update_sfsj(cfg)
    date = sorted([s for s in os.listdir(cfg.get("sfsj_saveroot")) if s.endswith("sfsj.txt")])[-1]
    date = date.split("_")[1]
    if not os.path.exists(f"{cfg.get('save_dir')}daily_{date}_spd_min5.txt"):
        datas = data.download(cfg)
        speed5 = spder.update_spdfile(datas, cfg)
    else:
        now = datetime.datetime.now().strftime("%H:%M:%S")
        print(f"[{now}] spped file alread downloaded!")

    op.update_extern_user(cfg.get("tdx_extern_file"), cfg.get("save_dir"))
    op.update_tdx_signal_file(cfg)

    return jsonify({"status": "success", "ztcount": len(sf_df)})


if __name__ == "__main__":
    app.run(debug=True, port=5000)
