from pytdx.hq import TdxHq_API
import os, sys, time
import pandas as pd, numpy as np


import datetime
import pandas as pd, numpy as np

### -----------------------------------> for s<PERSON><PERSON><PERSON><PERSON>an fetching
tdx_dir = 'C:/new_tdx_mock/'

def fetch_sfsj(tdx_dir):
    a = pd.read_json(f"{tdx_dir}T0002/cloud_cache/list/func_zdtfx101_1.jsn",encoding='gbk')
    cols = np.array(a['colheader'].values[0])
    b = np.array(a['data'].values[0])
    df = pd.DataFrame(b,columns=cols)
    df = df.loc[(~df.yy.str.contains('ST')) & (df['$SC']!='')]
    sf = df[['$SC','$ZQDM','ztsj1']].copy()
    sf['ztsj2'] = sf['ztsj1'].str.replace(':','')
    sf.columns = ['mkt','sym','str','value']
    return sf

sf_df = fetch_sfsj(tdx_dir)
today = str(datetime.date.today())


def save_sfsj(save_dir, sf_df, save_as_name):
    assert len(save_as_name)>0, 'save_as_name not provided!'
    sf_df.to_csv(f'{save_dir}{save_as_name}',header=None,index=None,sep='|')


### ---------------------------------------------------> for speed fetching


## read all syms each day
def read_stk_syms_from_tdxdir(tdx_dir: str, markets: list):
    all_syms = []
    for market in markets:
        daily_dir = f'{tdx_dir}{market}/lday/'
        all_syms.extend([s.split('.')[0][2:] for s in os.listdir(daily_dir)])
    return [s for s in all_syms if s[:2] in ['00','30','60','68']]


## download minBar from TDX server
def download_1sym_1minbar(sym,market,ndays,offset=0, verbose=False):
    all_data = []
    with api.connect('119.147.212.81', 7709):
        for i in range(ndays):
            num_bars = 240 if i<ndays-1 else 241
            data = api.to_df(api.get_security_bars(8,market,sym,240*(offset+i),num_bars))
            if data is None: 
                continue
            try:
                data = data[['datetime','open','close','high','low','vol','amount']]
            except:
                continue
            all_data.append(data)
            if verbose:
                print(f'downloading [{sym}]: {100*(i+1)/ndays:.2f} %',end='\r')
            # time.sleep(0.1)
    if len(all_data)==0: return None
    mgd = pd.concat(all_data[::-1])
    mgd['datetime'] = pd.to_datetime(mgd['datetime'])
    mgd['date'] = mgd['datetime'].dt.date
    return mgd.set_index(['date','datetime'])['close']

## calc_max rise speed from minBar close
def calc_max_spped(ndays, close, period):
    speed = close.pct_change(period).dropna(how='all').groupby('date').max() * 100
    return speed.iloc[-ndays:,:]

api = TdxHq_API()
tdx_dir = 'C:/new_tdx_mock/vipdoc/'

markets = ['sz', 'sh']
hssyms = read_stk_syms_from_tdxdir(tdx_dir, markets=['sz','sh'])
## batch download minBar for all sym over multi-days
from tabnanny import verbose


def batch_download(syms,
                   out_datas={}, 
                   ndays=1, 
                   offset=0, 
                   retry_interval=300):
    t00 = time.time()
    market_map = {'00': 0, '30': 0, ## shenzhen market 
                  '60':1, '68':1,   ## shanghai market
                  '43':2, '83':2, '87':2} ## beijing market
    # for i in range(retry_num):
    while True:
        try:
            for sym in syms:
                t0 = time.time()
                if sym in out_datas.keys():
                    continue
                market = market_map[sym[:2]]
                out_datas[sym] = download_1sym_1minbar(sym,market,ndays=ndays,offset=offset,verbose=False)
                pct = 100*(syms.index(sym)+1)/len(syms)
                t1 = time.time()
                time_remaining = (len(syms) - len(out_datas)) * (t1 - t0) / 60.0
                print(f'progress: {pct:.2f} %, time elapsed: {time_remaining:.2f} minutes',end='\r')
            break
        except:
            for i in range(retry_interval):
                print(f'\ntime elaspse to retry again: {retry_interval - i}', end='\r')
                time.sleep(1)
        
    
    

datas = {}
ndays, offset = 1, 0
batch_download(hssyms[:], datas, ndays=1, offset=0, retry_interval=300)
datas = {key:value for key,value in datas.items() if value is not None}
print('\ndownloaded items: ', len(datas))

close = pd.concat(datas,axis=1,keys=datas.keys()).sort_index()
speed2 = calc_max_spped(ndays, close, 2)
speed5 = calc_max_spped(ndays, close, 5)


## save results
def save_as_tdxfile(speed_df, save_dir='./', name_comment=''):
    def _market_map(sym):
        if sym[:2] in ['00','30']:
            return 0
        elif sym[:2] in ['60','68']:
            return 1
        elif sym[:2] in ['43','83','87']:
            return 2

    xs = []
    mgd = 'mgd_' if speed_df.shape[0] > 1 else 'daily_'
    for date in speed_df.index:
        x = speed_df.loc[date].dropna().to_frame().reset_index()
        x.columns = ['sym','value']
        x['mkt'] = x['sym'].apply(_market_map)
        x['date'] = str(date).replace('-','')
        x = x[['mkt','sym','date','value']]
        xs.append(x)
    pd.concat(xs).to_csv(f'{save_dir}{mgd}{date}_{name_comment}.txt',sep='|',header=None,index=None)

save_as_tdxfile(speed2, save_dir='./fetched_data/', name_comment='min2')
save_as_tdxfile(speed5, save_dir='./fetched_data/', name_comment='min5')
