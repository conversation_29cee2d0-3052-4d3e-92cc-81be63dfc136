<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>热点板块选股系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #121212;
            color: #e0e0e0;
        }

        .container {
            background-color: #1e1e1e;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #e0e0e0;
            text-align: left;
        }

        .form-group {
            margin-bottom: 20px;
        }

        select,
        button {
            padding: 10px 15px;
            font-size: 16px;
            border-radius: 4px;
            border: 1px solid #ddd;
            background-color: #333;
            color: #fff;
        }

        button {
            background-color: #4285f4;
            color: white;
            border: none;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #3367d6;
        }

        #result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            background-color: #2d2d2d;
            /* 深灰色结果区域 */
            display: none;
            color: #e0e0e0;
        }

        .stock-list {
            margin-top: 10px;
        }

        .stock-item {
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- <h1><button onclick="location.reload()">↻ 重置</button></h1> -->

        <div class="form-group">
            <select id="block-select">
                {% for option in options %}
                <option value="{{ option }}">{{ option }}</option>
                {% endfor %}
            </select>
            <button id="select-stocks-btn">板块选股</button>
            <button id="select-stocks-sc-btn">双创</button>
            <button id="select-stocks-bz-btn">北证</button>
            <button id="update-fengchui-btn">更新风吹</button>
            <button id="download-btn">下载数据</button>
            <button onclick="location.reload()">↻ 刷新重置</button>
        </div>

        <div id="result" style="display: none;">
            <p><span id="block-name"></span> （ 选中 <span id="stock-count"></span> ）</p>
            <!-- <div class="stock-list" id="stock-list"></div> -->
        </div>

        <div id="fengchui-result" style="display: none;">
            <p>（ 选中风吹 <span id="fengchui-count"></span> ）</p>
        </div>

        <div id="ztresult" style="display: none;">
            <p>（ 涨停家数 <span id="ztcount"></span> ）</p>
        </div>
    </div>

    <script>
        document.getElementById('select-stocks-btn').addEventListener('click', function () {
            const blockName = document.getElementById('block-select').value;

            fetch('/select_stocks', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `block_name=${encodeURIComponent(blockName)}`
            })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        document.getElementById('block-name').textContent = data.block_name;
                        document.getElementById('stock-count').textContent = data.count;
                        document.getElementById('result').style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('选股过程中发生错误');
                });
        });

        document.getElementById('select-stocks-sc-btn').addEventListener('click', function () {
            const blockName = document.getElementById('block-select').value;

            fetch('/select_sc_stocks', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `block_name=${encodeURIComponent(blockName)}`
            })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        document.getElementById('block-name').textContent = data.block_name;
                        document.getElementById('stock-count').textContent = data.count;
                        document.getElementById('result').style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('选股过程中发生错误');
                });
        });

        document.getElementById('select-stocks-bz-btn').addEventListener('click', function () {
            const blockName = document.getElementById('block-select').value;

            fetch('/select_bz_stocks', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `block_name=${encodeURIComponent(blockName)}`
            })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        document.getElementById('block-name').textContent = data.block_name;
                        document.getElementById('stock-count').textContent = data.count;
                        document.getElementById('result').style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('选股过程中发生错误');
                });
        });


        document.getElementById('update-fengchui-btn').addEventListener('click', function () {
            fetch('/update_fengchui', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        document.getElementById('fengchui-count').textContent = data.count;
                        document.getElementById("fengchui-result").style.display = "block";
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('更新风吹出错');
                });
        });

        document.getElementById('download-btn').addEventListener('click', function () {
            this.textContent = '数据下载中...';
            this.disabled = true;
            fetch('/data_fetcher', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                }
            })

                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        document.getElementById('ztcount').textContent = data.ztcount;
                        document.getElementById("ztresult").style.display = "block";
                    }
                    this.textContent='下载完成';
                    this.disabled=false;
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('下载数据出错');
                });
        });
    </script>
</body>

</html>