#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Utility functions for working with lcidx.lii files
"""

from lcidx_parser import LcidxParser, read_lcidx_file, write_lcidx_file
from typing import List, Dict, Union, Optional
import pandas as pd


def lcidx_to_dataframe(file_path: str) -> pd.DataFrame:
    """
    Convert lcidx.lii file to pandas DataFrame
    
    Args:
        file_path: Path to the lcidx.lii file
        
    Returns:
        pandas DataFrame with 'code' and 'name' columns
    """
    records = read_lcidx_file(file_path)
    return pd.DataFrame(records)


def dataframe_to_lcidx(df: pd.DataFrame, file_path: str, 
                       code_col: str = 'code', name_col: str = 'name') -> None:
    """
    Save pandas DataFrame to lcidx.lii file
    
    Args:
        df: pandas DataFrame
        file_path: Path to save the lcidx.lii file
        code_col: Column name for stock codes
        name_col: Column name for stock names
    """
    records = []
    for _, row in df.iterrows():
        records.append({
            'code': str(row[code_col]),
            'name': str(row[name_col])
        })
    write_lcidx_file(file_path, records)


def search_by_code(file_path: str, code: str) -> Optional[Dict[str, str]]:
    """
    Search for a record by stock code
    
    Args:
        file_path: Path to the lcidx.lii file
        code: Stock code to search for
        
    Returns:
        Dictionary with 'code' and 'name' if found, None otherwise
    """
    records = read_lcidx_file(file_path)
    for record in records:
        if record['code'] == code:
            return record
    return None


def search_by_name(file_path: str, name: str, partial: bool = True) -> List[Dict[str, str]]:
    """
    Search for records by stock name
    
    Args:
        file_path: Path to the lcidx.lii file
        name: Stock name to search for
        partial: If True, perform partial matching
        
    Returns:
        List of matching records
    """
    records = read_lcidx_file(file_path)
    matches = []
    
    for record in records:
        if partial:
            if name.lower() in record['name'].lower():
                matches.append(record)
        else:
            if record['name'] == name:
                matches.append(record)
    
    return matches


def merge_lcidx_files(file_paths: List[str], output_path: str, 
                      remove_duplicates: bool = True) -> None:
    """
    Merge multiple lcidx.lii files into one
    
    Args:
        file_paths: List of input file paths
        output_path: Output file path
        remove_duplicates: If True, remove duplicate codes
    """
    all_records = []
    seen_codes = set()
    
    for file_path in file_paths:
        try:
            records = read_lcidx_file(file_path)
            for record in records:
                if remove_duplicates:
                    if record['code'] not in seen_codes:
                        all_records.append(record)
                        seen_codes.add(record['code'])
                else:
                    all_records.append(record)
        except Exception as e:
            print(f"Warning: Could not read {file_path}: {e}")
    
    write_lcidx_file(output_path, all_records)


def print_file_info(file_path: str) -> None:
    """
    Print information about a lcidx.lii file
    
    Args:
        file_path: Path to the lcidx.lii file
    """
    try:
        records = read_lcidx_file(file_path)
        print(f"File: {file_path}")
        print(f"Total records: {len(records)}")
        
        if records:
            print(f"First record: Code='{records[0]['code']}', Name='{records[0]['name']}'")
            if len(records) > 1:
                print(f"Last record: Code='{records[-1]['code']}', Name='{records[-1]['name']}'")
            
            # Show some statistics
            codes = [r['code'] for r in records if r['code']]
            names = [r['name'] for r in records if r['name']]
            
            if codes:
                print(f"Code length range: {min(len(c) for c in codes)} - {max(len(c) for c in codes)}")
            if names:
                print(f"Name length range: {min(len(n) for n in names)} - {max(len(n) for n in names)}")
        
    except Exception as e:
        print(f"Error reading file {file_path}: {e}")


# Example usage functions
def example_usage():
    """Show example usage of the lcidx utilities"""
    
    file_path = r"C:\new_tdx\T0002\lc\lcidx.lii"
    
    print("=== Example Usage ===")
    
    # 1. Read file and show info
    print("\n1. File information:")
    print_file_info(file_path)
    
    # 2. Search by code
    print("\n2. Search by code '393001':")
    result = search_by_code(file_path, '393001')
    if result:
        print(f"Found: {result}")
    else:
        print("Not found")
    
    # 3. Search by name
    print("\n3. Search by name containing '储能':")
    results = search_by_name(file_path, '储能')
    for result in results:
        print(f"Found: {result}")
    
    # 4. Convert to DataFrame (requires pandas)
    try:
        print("\n4. Convert to DataFrame:")
        df = lcidx_to_dataframe(file_path)
        print(df.head())
    except ImportError:
        print("pandas not available, skipping DataFrame example")
    
    # 5. Create a new file with sample data
    print("\n5. Creating sample file:")
    sample_records = [
        {'code': '000001', 'name': '平安银行'},
        {'code': '000002', 'name': '万科A'},
        {'code': '393001', 'name': '新储能'},
    ]
    
    sample_file = 'sample_lcidx.lii'
    write_lcidx_file(sample_file, sample_records)
    print(f"Created {sample_file}")
    print_file_info(sample_file)
    
    # Clean up
    import os
    if os.path.exists(sample_file):
        os.remove(sample_file)
        print(f"Cleaned up {sample_file}")


if __name__ == "__main__":
    example_usage()
