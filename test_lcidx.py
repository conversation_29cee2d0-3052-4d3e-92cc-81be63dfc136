#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for lcidx_parser.py
"""

from lcidx_parser import read_lcidx_file, write_lcidx_file, LcidxParser
import os

def test_parser():
    """Test the lcidx parser functionality"""
    
    # Test file path
    test_file = 'test_lcidx.lii'
    
    try:
        # Test data
        test_records = [
            {'code': '000001', 'name': '平安银行'},
            {'code': '000002', 'name': '万科A'},
            ['000003', '国农科技'],  # Test list format
            {'code': '393001', 'name': '新储能'},  # From original file
        ]
        
        print('Testing write function...')
        write_lcidx_file(test_file, test_records)
        print(f'Successfully wrote {len(test_records)} records to {test_file}')
        
        print('\nTesting read function...')
        read_records = read_lcidx_file(test_file)
        
        print(f'Written {len(test_records)} records, read {len(read_records)} records:')
        for i, record in enumerate(read_records):
            print(f'  {i+1}: Code="{record["code"]}", Name="{record["name"]}"')
        
        # Test adding a record
        print('\nTesting add_record function...')
        parser = LcidxParser(test_file)
        parser.add_record('600000', '浦发银行')
        
        updated_records = read_lcidx_file(test_file)
        print(f'After adding one record, total records: {len(updated_records)}')
        print(f'Last record: Code="{updated_records[-1]["code"]}", Name="{updated_records[-1]["name"]}"')
        
        print('\nTest completed successfully!')
        
    except Exception as e:
        print(f'Test failed with error: {e}')
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f'Cleaned up test file: {test_file}')


def test_original_file():
    """Test reading the original file"""
    
    original_file = r"C:\new_tdx\T0002\lc\lcidx.lii"
    
    try:
        print(f'Reading original file: {original_file}')
        records = read_lcidx_file(original_file)
        
        print(f'Found {len(records)} records in original file:')
        for i, record in enumerate(records):
            print(f'  {i+1}: Code="{record["code"]}", Name="{record["name"]}"')
            
    except Exception as e:
        print(f'Failed to read original file: {e}')


if __name__ == "__main__":
    print("=== Testing lcidx parser ===")
    test_parser()
    
    print("\n=== Testing original file ===")
    test_original_file()
