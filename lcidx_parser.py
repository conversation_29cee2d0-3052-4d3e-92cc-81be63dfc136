#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Parser for lcidx.lii files from TDX
File format appears to be fixed-length records of 64 bytes each
Each record contains:
- 4 bytes: unknown header (seems to be 01000000 for valid records)
- 6 bytes: stock code (ASCII)
- ~20 bytes: stock name (GBK encoded)
- remaining bytes: padding/other data
"""

import struct
import os
from typing import List, Dict, Union


class LcidxParser:
    """Parser for lcidx.lii files"""
    
    RECORD_SIZE = 64
    CODE_OFFSET = 4
    CODE_SIZE = 6
    NAME_OFFSET = 10
    NAME_SIZE = 20
    
    def __init__(self, file_path: str):
        self.file_path = file_path
    
    def read_file(self) -> List[Dict[str, str]]:
        """
        Read and parse the lcidx.lii file
        
        Returns:
            List of dictionaries, each containing 'code' and 'name' keys
        """
        records = []
        
        if not os.path.exists(self.file_path):
            raise FileNotFoundError(f"File not found: {self.file_path}")
        
        with open(self.file_path, 'rb') as f:
            data = f.read()
        
        # Process records
        pos = 0
        while pos + self.RECORD_SIZE <= len(data):
            record_data = data[pos:pos + self.RECORD_SIZE]
            
            # Check if this is a valid record (first 4 bytes should be 01000000 for valid records)
            header = record_data[:4]
            if header == b'\x01\x00\x00\x00':
                # Extract code (6 bytes starting at offset 4)
                code_bytes = record_data[self.CODE_OFFSET:self.CODE_OFFSET + self.CODE_SIZE]
                code = code_bytes.decode('ascii', errors='ignore').rstrip('\x00')
                
                # Extract name (GBK encoded, starting at offset 10)
                name_bytes = record_data[self.NAME_OFFSET:self.NAME_OFFSET + self.NAME_SIZE]
                name_bytes = name_bytes.rstrip(b'\x00')  # Remove null padding
                
                try:
                    name = name_bytes.decode('gbk', errors='ignore')
                except UnicodeDecodeError:
                    name = name_bytes.decode('gbk', errors='replace')
                
                # Only add non-empty records
                if code.strip() or name.strip():
                    records.append({
                        'code': code.strip(),
                        'name': name.strip()
                    })
            
            pos += self.RECORD_SIZE
        
        return records
    
    def write_file(self, records: List[Union[Dict[str, str], List[str]]]) -> None:
        """
        Write records to the lcidx.lii file
        
        Args:
            records: List of dictionaries with 'code' and 'name' keys,
                    or list of lists/tuples with [code, name]
        """
        # Normalize input format
        normalized_records = []
        for record in records:
            if isinstance(record, dict):
                code = record.get('code', '')
                name = record.get('name', '')
            elif isinstance(record, (list, tuple)) and len(record) >= 2:
                code = str(record[0])
                name = str(record[1])
            else:
                raise ValueError(f"Invalid record format: {record}")
            
            normalized_records.append({'code': code, 'name': name})
        
        # Calculate total file size (pad to multiple of RECORD_SIZE)
        total_records = max(len(normalized_records), 1)
        # Round up to ensure we have enough space
        if len(normalized_records) * self.RECORD_SIZE < 320:  # Minimum file size observed
            total_records = 320 // self.RECORD_SIZE
        
        # Create binary data
        data = bytearray(total_records * self.RECORD_SIZE)
        
        for i, record in enumerate(normalized_records):
            if i >= total_records:
                break
                
            offset = i * self.RECORD_SIZE
            
            # Write header (01000000 for valid records)
            data[offset:offset + 4] = b'\x01\x00\x00\x00'
            
            # Write code (6 bytes, ASCII)
            code = record['code'][:self.CODE_SIZE]  # Truncate if too long
            code_bytes = code.encode('ascii', errors='ignore')
            data[offset + self.CODE_OFFSET:offset + self.CODE_OFFSET + len(code_bytes)] = code_bytes
            
            # Write name (GBK encoded)
            name = record['name']
            try:
                name_bytes = name.encode('gbk', errors='ignore')
            except UnicodeEncodeError:
                name_bytes = name.encode('gbk', errors='replace')
            
            # Truncate name if too long
            if len(name_bytes) > self.NAME_SIZE:
                name_bytes = name_bytes[:self.NAME_SIZE]
            
            data[offset + self.NAME_OFFSET:offset + self.NAME_OFFSET + len(name_bytes)] = name_bytes
        
        # Write to file
        with open(self.file_path, 'wb') as f:
            f.write(data)
    
    def add_record(self, code: str, name: str) -> None:
        """
        Add a new record to the file
        
        Args:
            code: Stock code
            name: Stock name
        """
        # Read existing records
        try:
            records = self.read_file()
        except FileNotFoundError:
            records = []
        
        # Add new record
        records.append({'code': code, 'name': name})
        
        # Write back to file
        self.write_file(records)


# Convenience functions for direct use
def read_lcidx_file(file_path: str) -> List[Dict[str, str]]:
    """
    Read and parse a lcidx.lii file
    
    Args:
        file_path: Path to the lcidx.lii file
        
    Returns:
        List of dictionaries with 'code' and 'name' keys
    """
    parser = LcidxParser(file_path)
    return parser.read_file()


def write_lcidx_file(file_path: str, records: List[Union[Dict[str, str], List[str]]]) -> None:
    """
    Write records to a lcidx.lii file
    
    Args:
        file_path: Path to the lcidx.lii file
        records: List of records (dicts or lists)
    """
    parser = LcidxParser(file_path)
    parser.write_file(records)


if __name__ == "__main__":
    # Example usage
    file_path = r"C:\new_tdx\T0002\lc\lcidx.lii"
    
    try:
        # Read the file
        print("Reading file...")
        records = read_lcidx_file(file_path)
        
        print(f"Found {len(records)} records:")
        for i, record in enumerate(records[:10]):  # Show first 10 records
            print(f"  {i+1}: Code='{record['code']}', Name='{record['name']}'")
        
        if len(records) > 10:
            print(f"  ... and {len(records) - 10} more records")
            
    except Exception as e:
        print(f"Error: {e}")
