import tkinter as tk
from tkinter import ttk, scrolledtext, filedialog, messagebox
import os

class FileViewerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("File Viewer")
        self.root.geometry("800x600")
        
        # Create main frame
        self.main_frame = ttk.Frame(self.root, padding="10")
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Create widgets
        self.create_widgets()
        
        # Current file path
        self.current_file = None

    def create_widgets(self):
        # File path frame
        path_frame = ttk.Frame(self.main_frame)
        path_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        self.path_var = tk.StringVar()
        path_entry = ttk.Entry(path_frame, textvariable=self.path_var, width=60)
        path_entry.grid(row=0, column=0, padx=(0, 5))
        
        browse_btn = ttk.Button(path_frame, text="Browse", command=self.browse_file)
        browse_btn.grid(row=0, column=1)
        
        # Text area for file content
        self.text_area = scrolledtext.ScrolledText(self.main_frame, wrap=tk.WORD, width=70, height=30)
        self.text_area.grid(row=1, column=0, columnspan=3, pady=10)
        
        # Control buttons
        btn_frame = ttk.Frame(self.main_frame)
        btn_frame.grid(row=2, column=0, columnspan=3, pady=5)
        
        save_btn = ttk.Button(btn_frame, text="Save", command=self.save_file)
        save_btn.grid(row=0, column=0, padx=5)
        
        clear_btn = ttk.Button(btn_frame, text="Clear", command=self.clear_content)
        clear_btn.grid(row=0, column=1, padx=5)
        
        refresh_btn = ttk.Button(btn_frame, text="Refresh", command=self.refresh_content)
        refresh_btn.grid(row=0, column=2, padx=5)

    def browse_file(self):
        file_path = filedialog.askopenfilename(
            filetypes=[
                ("Text files", "*.txt"),
                ("Python files", "*.py"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self.current_file = file_path
            self.path_var.set(file_path)
            self.load_file_content()

    def load_file_content(self):
        try:
            with open(self.current_file, 'r', encoding='utf-8') as file:
                content = file.read()
                self.text_area.delete(1.0, tk.END)
                self.text_area.insert(tk.END, content)
        except Exception as e:
            messagebox.showerror("Error", f"Error reading file: {str(e)}")

    def save_file(self):
        if not self.current_file:
            self.current_file = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[
                    ("Text files", "*.txt"),
                    ("Python files", "*.py"),
                    ("All files", "*.*")
                ]
            )
        if self.current_file:
            try:
                content = self.text_area.get(1.0, tk.END)
                with open(self.current_file, 'w', encoding='utf-8') as file:
                    file.write(content)
                messagebox.showinfo("Success", "File saved successfully!")
            except Exception as e:
                messagebox.showerror("Error", f"Error saving file: {str(e)}")

    def clear_content(self):
        self.text_area.delete(1.0, tk.END)
        self.current_file = None
        self.path_var.set("")

    def refresh_content(self):
        if self.current_file:
            self.load_file_content()

def main():
    root = tk.Tk()
    app = FileViewerApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
